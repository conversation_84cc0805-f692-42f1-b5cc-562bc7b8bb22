from django.test import TestCase, Client, tag
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth.models import User
from datetime import timedelta
import json

from packet_analyzer.models import DownlinkPacket
from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile


class DownlinkViewTestBase(TestCase):
    """Base class for downlink view tests"""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )

        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 23.5, "lng": 58.4}]),
            colr="#FF5733",
            covr=10.5,
            loca="Test Location",
            work_shifts={}
        )

        # Create test devices
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Device Description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        self.gateway = Device.objects.create(
            name="Test Gateway",
            desc="Test Gateway Description",
            euid="9876543210ABCDEF",
            type="Whiskers Gateway V1",
            fild=self.field
        )

        # Create user profile and assign devices
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            role="User",
            titl="Test Title",
            orgn="Test Organization"
        )
        self.user_profile.devs.add(self.device, self.gateway)

        # Create test client
        self.client = Client()

        # Create downlink packets
        self.now = timezone.now()
        for i in range(15):  # Create 15 packets to test pagination
            DownlinkPacket.objects.create(
                devi=self.device,
                gate=self.gateway,
                data=f"DATA{i}",
                deco={"test": f"data{i}"},
                cont=i,
                chan=1,
                freq=868,
                txpr=14.0 + (i * 0.1),
                crat=self.now - timedelta(minutes=30 - i),
                txat=self.now - timedelta(minutes=25 - i)
            )


@tag('mock')
class DownlinkListViewMockTest(DownlinkViewTestBase):
    """Test DownlinkListView with mocks"""

    def test_downlink_list_view_requires_login(self):
        """Test that the downlink list view requires login"""
        response = self.client.get(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 302)  # Redirects to login page
        self.assertIn('login', response.url)

    def test_downlink_list_view_with_incorrect_credentials(self):
        """Test that the downlink list view rejects incorrect credentials"""
        # Try to login with incorrect password
        login_success = self.client.login(username='testuser', password='wrongpassword')
        self.assertFalse(login_success)

        # Try to access the view after failed login
        response = self.client.get(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 302)  # Still redirects to login page
        self.assertIn('login', response.url)

    def test_downlink_list_view_with_login(self):
        """Test that the downlink list view works with login"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'packet_analyzer/downlink_list.html')

    def test_downlink_list_view_context(self):
        """Test that the downlink list view provides the correct context"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:downlink_list'))

        # Check that the context contains the packet list
        self.assertIn('packet_list', response.context)

        # Check that the context contains the paginator
        self.assertIn('paginator', response.context)

        # Check that the paginator has the correct number of pages
        # With 15 packets and 10 per page, we should have 2 pages
        self.assertEqual(response.context['paginator'].num_pages, 2)

    def test_downlink_list_view_pagination(self):
        """Test that the downlink list view paginates correctly"""
        self.client.login(username='testuser', password='testpassword')

        # Test first page
        response = self.client.get(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(len(response.context['packet_list']), 10)

        # Test second page
        response = self.client.get(reverse('packet_analyzer:downlink_list') + '?page=2')
        self.assertEqual(len(response.context['packet_list']), 5)

    def test_downlink_list_view_ordering(self):
        """Test that the downlink list view orders the queryset correctly"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:downlink_list'))

        # Check that the queryset is ordered by txat in descending order
        packets = list(response.context['packet_list'])
        for i in range(len(packets) - 1):
            self.assertGreaterEqual(packets[i].txat, packets[i + 1].txat)


@tag('unmock')
class DownlinkListViewUnmockTest(DownlinkViewTestBase):
    """Test DownlinkListView with real database objects"""

    def test_downlink_list_view_with_real_login(self):
        """Test that the downlink list view works with real login"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 200)

        # Check that the response contains the expected data
        self.assertContains(response, 'Test Device')
        self.assertContains(response, 'Test Gateway')

    def test_downlink_list_view_with_invalid_page(self):
        """Test that the downlink list view handles invalid page numbers gracefully"""
        self.client.login(username='testuser', password='testpassword')

        # Test with a page number that's too high
        response = self.client.get(reverse('packet_analyzer:downlink_list') + '?page=999')
        self.assertEqual(response.status_code, 404)

        # Test with a non-numeric page number
        response = self.client.get(reverse('packet_analyzer:downlink_list') + '?page=abc')
        self.assertEqual(response.status_code, 404)

    def test_downlink_list_view_with_empty_database(self):
        """Test that the downlink list view handles an empty database gracefully"""
        # Delete all downlink packets
        DownlinkPacket.objects.all().delete()

        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 200)

        # Check that the context contains an empty packet list
        self.assertEqual(len(response.context['packet_list']), 0)

    def test_downlink_list_view_with_different_user_permissions(self):
        """Test that the downlink list view shows only packets for devices the user has access to"""
        # Create a new user without access to any devices
        new_user = User.objects.create_user(
            username='newuser',
            password='newpassword',
            email='<EMAIL>'
        )

        new_user_profile = UserProfile.objects.create(
            user=new_user,
            role="User",
            titl="New User",
            orgn="Test Organization"
        )

        # Create a new device and packet that the new user doesn't have access to
        new_device = Device.objects.create(
            name="New Device",
            desc="New Device Description",
            euid="1111222233334444",
            type="Whiskers Node V1",
            fild=self.field
        )

        DownlinkPacket.objects.create(
            devi=new_device,
            gate=self.gateway,
            data="NEWDATA",
            deco={"test": "new_data"},
            cont=99,
            chan=1,
            freq=868,
            txpr=14.0,
            crat=self.now,
            txat=self.now
        )

        # Login as the new user
        self.client.login(username='newuser', password='newpassword')
        response = self.client.get(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 200)

        # Check that the response contains the packet list
        self.assertIn('packet_list', response.context)

        # Now give the new user access to the new device
        new_user_profile.devs.add(new_device)

        # The new user should now be able to see the view with the updated device access
        response = self.client.get(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 200)

    def test_downlink_list_view_with_http_methods(self):
        """Test that the downlink list view handles different HTTP methods appropriately"""
        self.client.login(username='testuser', password='testpassword')

        # Test with POST method (should not be allowed)
        response = self.client.post(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 405)  # Method Not Allowed

        # Test with PUT method (should not be allowed)
        response = self.client.put(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 405)  # Method Not Allowed

        # Test with DELETE method (should not be allowed)
        response = self.client.delete(reverse('packet_analyzer:downlink_list'))
        self.assertEqual(response.status_code, 405)  # Method Not Allowed
