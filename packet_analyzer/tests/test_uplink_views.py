from django.test import TestCase, Client, tag
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth.models import User
from datetime import timedelta
import json

from packet_analyzer.models import UplinkPacket
from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile


class UplinkViewTestBase(TestCase):
    """Base class for uplink view tests"""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )

        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 23.5, "lng": 58.4}]),
            colr="#FF5733",
            covr=10.5,
            loca="Test Location",
            work_shifts={}
        )

        # Create test devices
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Device Description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        self.gateway = Device.objects.create(
            name="Test Gateway",
            desc="Test Gateway Description",
            euid="9876543210ABCDEF",
            type="Whiskers Gateway V1",
            fild=self.field
        )

        # Create user profile and assign devices
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            role="User",
            titl="Test Title",
            orgn="Test Organization"
        )
        self.user_profile.devs.add(self.device, self.gateway)

        # Create test client
        self.client = Client()

        # Create uplink packets
        self.now = timezone.now()
        for i in range(15):  # Create 15 packets to test pagination
            UplinkPacket.objects.create(
                devi=self.device,
                gate=self.gateway,
                data=f"DATA{i}",
                deco={"test": f"data{i}"},
                cont=i,
                rssi=-70 + i,
                snr=5.5 + (i * 0.1),
                chan=1,
                freq=868,
                txat=self.now - timedelta(minutes=30 - i),
                rxat=self.now - timedelta(minutes=25 - i)
            )


@tag('mock')
class UplinkListViewMockTest(UplinkViewTestBase):
    """Test UplinkListView with mocks"""

    def test_uplink_list_view_requires_login(self):
        """Test that the uplink list view requires login"""
        response = self.client.get(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 302)  # Redirects to login page
        self.assertIn('login', response.url)

    def test_uplink_list_view_with_incorrect_credentials(self):
        """Test that the uplink list view rejects incorrect credentials"""
        # Try to login with incorrect password
        login_success = self.client.login(username='testuser', password='wrongpassword')
        self.assertFalse(login_success)

        # Try to access the view after failed login
        response = self.client.get(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 302)  # Still redirects to login page
        self.assertIn('login', response.url)

    def test_uplink_list_view_with_nonexistent_user(self):
        """Test that the uplink list view rejects nonexistent users"""
        # Try to login with a nonexistent user
        login_success = self.client.login(username='nonexistentuser', password='password')
        self.assertFalse(login_success)

        # Try to access the view after failed login
        response = self.client.get(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 302)  # Still redirects to login page
        self.assertIn('login', response.url)

    def test_uplink_list_view_with_login(self):
        """Test that the uplink list view works with login"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'packet_analyzer/uplink_list.html')

    def test_uplink_list_view_context(self):
        """Test that the uplink list view provides the correct context"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:uplink_list'))

        # Check that the context contains the packet list
        self.assertIn('packet_list', response.context)

        # Check that the context contains the paginator
        self.assertIn('paginator', response.context)

        # Check that the paginator has the correct number of pages
        # With 15 packets and 10 per page, we should have 2 pages
        self.assertEqual(response.context['paginator'].num_pages, 2)

    def test_uplink_list_view_pagination(self):
        """Test that the uplink list view paginates correctly"""
        self.client.login(username='testuser', password='testpassword')

        # Test first page
        response = self.client.get(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(len(response.context['packet_list']), 10)

        # Test second page
        response = self.client.get(reverse('packet_analyzer:uplink_list') + '?page=2')
        self.assertEqual(len(response.context['packet_list']), 5)

    def test_uplink_list_view_queryset_filtering(self):
        """Test that the uplink list view filters the queryset correctly"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:uplink_list'))

        # Check that the queryset is filtered for the last 24 hours
        cutoff_time = timezone.now() - timedelta(hours=24)
        for packet in response.context['packet_list']:
            self.assertGreaterEqual(packet.rxat, cutoff_time)

    def test_uplink_list_view_deferred_fields(self):
        """Test that the uplink list view defers the data and deco fields"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:uplink_list'))

        # Check that the queryset defers the data and deco fields
        # This is a bit tricky to test directly, but we can check that the fields are loaded
        # when accessed
        packet = response.context['packet_list'][0]
        self.assertTrue(hasattr(packet, 'data'))
        self.assertTrue(hasattr(packet, 'deco'))


@tag('unmock')
class UplinkListViewUnmockTest(UplinkViewTestBase):
    """Test UplinkListView with real database objects"""

    def test_uplink_list_view_with_real_login(self):
        """Test that the uplink list view works with real login"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 200)

        # Check that the response contains the expected data
        self.assertContains(response, 'Test Device')
        self.assertContains(response, 'Test Gateway')

    def test_uplink_list_view_with_invalid_page(self):
        """Test that the uplink list view handles invalid page numbers gracefully"""
        self.client.login(username='testuser', password='testpassword')

        # Test with a page number that's too high
        response = self.client.get(reverse('packet_analyzer:uplink_list') + '?page=999')
        self.assertEqual(response.status_code, 404)

        # Test with a non-numeric page number
        response = self.client.get(reverse('packet_analyzer:uplink_list') + '?page=abc')
        self.assertEqual(response.status_code, 404)

    def test_uplink_list_view_with_empty_database(self):
        """Test that the uplink list view handles an empty database gracefully"""
        # Delete all uplink packets
        UplinkPacket.objects.all().delete()

        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 200)

        # Check that the context contains an empty packet list
        self.assertEqual(len(response.context['packet_list']), 0)

    def test_uplink_list_view_with_old_packets(self):
        """Test that the uplink list view filters out packets older than 24 hours"""
        # Create a packet that's older than 24 hours
        old_packet = UplinkPacket.objects.create(
            devi=self.device,
            gate=self.gateway,
            data="OLDDATA",
            deco={"test": "old_data"},
            cont=99,
            rssi=-70,
            snr=5.5,
            chan=1,
            freq=868,
            txat=self.now - timedelta(hours=25),
            rxat=self.now - timedelta(hours=25)
        )

        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:uplink_list'))

        # The old packet should not be in the queryset
        packet_ids = [p.id for p in response.context['packet_list']]
        self.assertNotIn(old_packet.id, packet_ids)

    def test_uplink_list_view_with_superuser(self):
        """Test that the uplink list view works with superuser"""
        # Create a superuser
        superuser = User.objects.create_superuser(
            username='admin',
            password='adminpassword',
            email='<EMAIL>'
        )

        # Create a user profile for the superuser
        UserProfile.objects.create(
            user=superuser,
            role="Admin",
            titl="Admin Title",
            orgn="Admin Organization"
        )

        # Login as superuser
        self.client.login(username='admin', password='adminpassword')
        response = self.client.get(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 200)

        # Just check that the response contains the packet list
        self.assertIn('packet_list', response.context)

    def test_uplink_list_view_with_http_methods(self):
        """Test that the uplink list view handles different HTTP methods appropriately"""
        self.client.login(username='testuser', password='testpassword')

        # Test with POST method (should not be allowed)
        response = self.client.post(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 405)  # Method Not Allowed

        # Test with PUT method (should not be allowed)
        response = self.client.put(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 405)  # Method Not Allowed

        # Test with DELETE method (should not be allowed)
        response = self.client.delete(reverse('packet_analyzer:uplink_list'))
        self.assertEqual(response.status_code, 405)  # Method Not Allowed

    def test_uplink_list_view_with_malformed_url(self):
        """Test that the uplink list view handles malformed URLs gracefully"""
        self.client.login(username='testuser', password='testpassword')

        # Test with malformed query parameters
        response = self.client.get(reverse('packet_analyzer:uplink_list') + '?page=1&invalid=param')
        self.assertEqual(response.status_code, 200)  # Should ignore invalid parameters
